@props([
    'selected' => 'ri-check-line',
    'name' => 'icon_class',
    'required' => true,
    'label' => 'Select Icon',
    'id' => null
])

@php
    // Generate unique ID if not provided
    $componentId = $id ?? 'icon-selector-' . uniqid();
    $inputId = $name;
    $displayId = $componentId . '-display';
    $tabsId = $componentId . '-tabs';
    $remixTabId = $componentId . '-remix-tab';
    $boxiconsTabId = $componentId . '-boxicons-tab';
    $remixContentId = $componentId . '-remix-icons';
    $boxiconsContentId = $componentId . '-boxicons-icons';

    // Get Remix Icons
    $remixCssPath = public_path('assets/icon-fonts/RemixIcons/fonts/remixicon.css');
    $remixIcons = [];
    if (file_exists($remixCssPath)) {
        $css = file_get_contents($remixCssPath);
        preg_match_all('/\.((ri-[a-z0-9\-]+-line))\:before/', $css, $matches);
        $remixIcons = array_unique($matches[1]);
        sort($remixIcons);
    }

    // Get Boxicons (basic and brands) - Using CDN icons list
    $boxicons = [];

    // Regular icons (bx-) - extracted from CDN
    $regularIcons = [
        'bx-8-ball', 'bx-a-arrow-down', 'bx-a-arrow-up', 'bx-accessibility', 'bx-acorn', 'bx-address-book', 'bx-air-conditioner', 'bx-air', 'bx-airplay', 'bx-alarm-alt', 'bx-alarm-check', 'bx-alarm-exclamation', 'bx-alarm-minus', 'bx-alarm-plus', 'bx-alarm-slash', 'bx-alarm-z', 'bx-alarm', 'bx-album-covers', 'bx-alert-circle', 'bx-alert-octagon', 'bx-alert-shield', 'bx-alert-square', 'bx-alert-triangle', 'bx-alien', 'bx-align-center', 'bx-align-justify', 'bx-align-left', 'bx-align-right', 'bx-ambulance', 'bx-ampersand', 'bx-analyze', 'bx-anchor', 'bx-angle', 'bx-angry', 'bx-animation-bounce', 'bx-apartment', 'bx-approximate', 'bx-apps-alt', 'bx-apps', 'bx-arch', 'bx-archive-alt', 'bx-archive-arrow-down', 'bx-archive-arrow-up', 'bx-archive', 'bx-area', 'bx-arrow-back', 'bx-arrow-from-bottom', 'bx-arrow-from-left', 'bx-arrow-from-right', 'bx-arrow-from-top', 'bx-arrow-to-bottom', 'bx-arrow-to-left', 'bx-arrow-to-right', 'bx-arrow-to-top', 'bx-at', 'bx-atom', 'bx-award', 'bx-badge', 'bx-badge-check', 'bx-ball', 'bx-band-aid', 'bx-bar-chart', 'bx-bar-chart-alt', 'bx-bar-chart-alt-2', 'bx-bar-chart-square', 'bx-barcode', 'bx-barcode-reader', 'bx-baseball', 'bx-basket', 'bx-basketball', 'bx-bath', 'bx-battery', 'bx-battery-full', 'bx-battery-low', 'bx-bed', 'bx-beenhere', 'bx-beer', 'bx-bell', 'bx-bell-minus', 'bx-bell-off', 'bx-bell-plus', 'bx-bible', 'bx-bicycle', 'bx-bid-landscape', 'bx-bid-portrait', 'bx-binoculars', 'bx-bitcoin', 'bx-blanket', 'bx-block', 'bx-bluetooth', 'bx-body', 'bx-bold', 'bx-bomb', 'bx-bone', 'bx-bong', 'bx-book', 'bx-book-add', 'bx-book-alt', 'bx-book-bookmark', 'bx-book-content', 'bx-book-heart', 'bx-book-open', 'bx-book-reader', 'bx-bookmark', 'bx-bookmark-alt', 'bx-bookmark-alt-minus', 'bx-bookmark-alt-plus', 'bx-bookmark-heart', 'bx-bookmark-minus', 'bx-bookmark-plus', 'bx-bookmarks', 'bx-boombox', 'bx-border-all', 'bx-border-bottom', 'bx-border-inner', 'bx-border-left', 'bx-border-none', 'bx-border-outer', 'bx-border-radius', 'bx-border-right', 'bx-border-top', 'bx-bot', 'bx-bowl-hot', 'bx-bowl-rice', 'bx-bowling-ball', 'bx-box', 'bx-bracket', 'bx-braille', 'bx-brain', 'bx-briefcase', 'bx-briefcase-alt', 'bx-briefcase-alt-2', 'bx-brightness', 'bx-brightness-half', 'bx-broadcast', 'bx-brush', 'bx-brush-alt', 'bx-bug', 'bx-bug-alt', 'bx-building', 'bx-building-house', 'bx-buildings', 'bx-bulb', 'bx-bullseye', 'bx-buoy', 'bx-bus', 'bx-bus-school', 'bx-business', 'bx-cabinet', 'bx-cake', 'bx-calculator', 'bx-calendar', 'bx-calendar-alt', 'bx-calendar-check', 'bx-calendar-edit', 'bx-calendar-event', 'bx-calendar-exclamation', 'bx-calendar-heart', 'bx-calendar-minus', 'bx-calendar-plus', 'bx-calendar-star', 'bx-calendar-week', 'bx-calendar-x', 'bx-camera', 'bx-camera-home', 'bx-camera-movie', 'bx-camera-off', 'bx-candles', 'bx-capsule', 'bx-car', 'bx-card', 'bx-care', 'bx-caret-down', 'bx-caret-down-circle', 'bx-caret-down-square', 'bx-caret-left', 'bx-caret-left-circle', 'bx-caret-left-square', 'bx-caret-right', 'bx-caret-right-circle', 'bx-caret-right-square', 'bx-caret-up', 'bx-caret-up-circle', 'bx-caret-up-square', 'bx-carousel', 'bx-cart', 'bx-cart-add', 'bx-cart-alt', 'bx-cart-download', 'bx-cast', 'bx-category', 'bx-category-alt', 'bx-cctv', 'bx-certification', 'bx-chair', 'bx-chalkboard', 'bx-chart', 'bx-chat', 'bx-check', 'bx-check-circle', 'bx-check-double', 'bx-check-shield', 'bx-check-square', 'bx-checkbox', 'bx-checkbox-checked', 'bx-checkbox-minus', 'bx-checkbox-square', 'bx-cheese', 'bx-chess', 'bx-chevron-down', 'bx-chevron-down-circle', 'bx-chevron-down-square', 'bx-chevron-left', 'bx-chevron-left-circle', 'bx-chevron-left-square', 'bx-chevron-right', 'bx-chevron-right-circle', 'bx-chevron-right-square', 'bx-chevron-up', 'bx-chevron-up-circle', 'bx-chevron-up-square', 'bx-chevrons-down', 'bx-chevrons-left', 'bx-chevrons-right', 'bx-chevrons-up', 'bx-child', 'bx-chip', 'bx-church', 'bx-circle', 'bx-circle-half', 'bx-circle-quarter', 'bx-circle-three-quarter', 'bx-clinic', 'bx-clipboard', 'bx-closet', 'bx-cloud', 'bx-cloud-download', 'bx-cloud-drizzle', 'bx-cloud-lightning', 'bx-cloud-rain', 'bx-cloud-snow', 'bx-cloud-upload', 'bx-code', 'bx-code-alt', 'bx-code-block', 'bx-code-curly', 'bx-coffee', 'bx-coffee-togo', 'bx-cog', 'bx-coin', 'bx-coin-stack', 'bx-collapse', 'bx-collapse-alt', 'bx-collapse-horizontal', 'bx-collapse-vertical', 'bx-collection', 'bx-color', 'bx-color-fill', 'bx-columns', 'bx-command', 'bx-comment', 'bx-comment-add', 'bx-comment-check', 'bx-comment-detail', 'bx-comment-dots', 'bx-comment-edit', 'bx-comment-error', 'bx-comment-minus', 'bx-comment-x', 'bx-compass', 'bx-confused', 'bx-conversation', 'bx-cookie', 'bx-cool', 'bx-copy', 'bx-copy-alt', 'bx-copyright', 'bx-credit-card', 'bx-credit-card-alt', 'bx-credit-card-front', 'bx-crop', 'bx-cross', 'bx-crosshair', 'bx-crown', 'bx-cube', 'bx-cube-alt', 'bx-current-location', 'bx-customize', 'bx-cut', 'bx-cycling', 'bx-cylinder', 'bx-data', 'bx-desktop', 'bx-detail', 'bx-devices', 'bx-dialpad', 'bx-dialpad-alt', 'bx-diamond', 'bx-dice-1', 'bx-dice-2', 'bx-dice-3', 'bx-dice-4', 'bx-dice-5', 'bx-dice-6', 'bx-directions', 'bx-disc', 'bx-dislike', 'bx-dizzy', 'bx-dna', 'bx-dock-bottom', 'bx-dock-left', 'bx-dock-right', 'bx-dock-top', 'bx-dollar', 'bx-dollar-circle', 'bx-donate-blood', 'bx-donate-heart', 'bx-door-open', 'bx-dots-horizontal', 'bx-dots-horizontal-rounded', 'bx-dots-vertical', 'bx-dots-vertical-rounded', 'bx-doughnut-chart', 'bx-down-arrow', 'bx-down-arrow-alt', 'bx-down-arrow-circle', 'bx-download', 'bx-downvote', 'bx-drink', 'bx-droplet', 'bx-dumbbell', 'bx-duplicate', 'bx-edit', 'bx-edit-alt', 'bx-envelope', 'bx-envelope-open', 'bx-equalizer', 'bx-eraser', 'bx-error', 'bx-error-circle', 'bx-euro', 'bx-exclude', 'bx-expand', 'bx-expand-alt', 'bx-expand-horizontal', 'bx-expand-vertical', 'bx-export', 'bx-extension', 'bx-face', 'bx-fast-forward', 'bx-fast-forward-circle', 'bx-female', 'bx-female-sign', 'bx-file', 'bx-file-blank', 'bx-file-find', 'bx-file-plus', 'bx-film', 'bx-filter', 'bx-filter-alt', 'bx-fingerprint', 'bx-first-page', 'bx-flag', 'bx-folder', 'bx-folder-minus', 'bx-folder-open', 'bx-folder-plus', 'bx-font', 'bx-font-color', 'bx-font-family', 'bx-font-size', 'bx-food-menu', 'bx-food-tag', 'bx-football', 'bx-fork', 'bx-formation', 'bx-fridge', 'bx-fullscreen', 'bx-game', 'bx-gas-pump', 'bx-ghost', 'bx-gift', 'bx-git-branch', 'bx-git-commit', 'bx-git-compare', 'bx-git-merge', 'bx-git-pull-request', 'bx-git-repo-forked', 'bx-glasses', 'bx-glasses-alt', 'bx-globe', 'bx-globe-alt', 'bx-grid', 'bx-grid-alt', 'bx-grid-horizontal', 'bx-grid-small', 'bx-grid-vertical', 'bx-group', 'bx-handicap', 'bx-happy', 'bx-happy-alt', 'bx-happy-beaming', 'bx-happy-heart-eyes', 'bx-hard-hat', 'bx-hash', 'bx-hdd', 'bx-heading', 'bx-headphone', 'bx-health', 'bx-heart', 'bx-heart-circle', 'bx-heart-square', 'bx-help-circle', 'bx-hexagon', 'bx-hide', 'bx-highlight', 'bx-history', 'bx-hive', 'bx-home', 'bx-home-alt', 'bx-home-circle', 'bx-home-heart', 'bx-home-smile', 'bx-horizontal-center', 'bx-horizontal-left', 'bx-horizontal-right', 'bx-hotel', 'bx-hourglass', 'bx-house', 'bx-id-card', 'bx-image', 'bx-image-add', 'bx-image-alt', 'bx-images', 'bx-import', 'bx-inbox', 'bx-infinite', 'bx-info-circle', 'bx-info-square', 'bx-injection', 'bx-intersect', 'bx-italic', 'bx-joystick', 'bx-joystick-alt', 'bx-joystick-button', 'bx-key', 'bx-keyboard', 'bx-label', 'bx-landscape', 'bx-laptop', 'bx-last-page', 'bx-laugh', 'bx-layer', 'bx-layer-minus', 'bx-layer-plus', 'bx-layout', 'bx-leaf', 'bx-left-arrow', 'bx-left-arrow-alt', 'bx-left-arrow-circle', 'bx-left-down-arrow-circle', 'bx-left-indent', 'bx-left-top-arrow-circle', 'bx-lemon', 'bx-library', 'bx-like', 'bx-line-chart', 'bx-line-chart-down', 'bx-link', 'bx-link-alt', 'bx-link-external', 'bx-list-check', 'bx-list-minus', 'bx-list-ol', 'bx-list-plus', 'bx-list-ul', 'bx-loader', 'bx-loader-alt', 'bx-loader-circle', 'bx-location-plus', 'bx-lock', 'bx-lock-alt', 'bx-lock-open', 'bx-lock-open-alt', 'bx-log-in', 'bx-log-in-circle', 'bx-log-out', 'bx-log-out-circle', 'bx-low-vision', 'bx-magnet', 'bx-mail-send', 'bx-male', 'bx-male-female', 'bx-male-sign', 'bx-map', 'bx-map-alt', 'bx-map-pin', 'bx-mask', 'bx-math', 'bx-medal', 'bx-medical-cross', 'bx-meh', 'bx-meh-alt', 'bx-meh-blank', 'bx-memory-card', 'bx-menu', 'bx-menu-alt-left', 'bx-menu-alt-right', 'bx-merge', 'bx-message', 'bx-message-add', 'bx-message-alt', 'bx-message-alt-add', 'bx-message-alt-check', 'bx-message-alt-detail', 'bx-message-alt-dots', 'bx-message-alt-edit', 'bx-message-alt-error', 'bx-message-alt-minus', 'bx-message-alt-x', 'bx-message-check', 'bx-message-detail', 'bx-message-dots', 'bx-message-edit', 'bx-message-error', 'bx-message-minus', 'bx-message-rounded', 'bx-message-rounded-add', 'bx-message-rounded-check', 'bx-message-rounded-detail', 'bx-message-rounded-dots', 'bx-message-rounded-edit', 'bx-message-rounded-error', 'bx-message-rounded-minus', 'bx-message-rounded-x', 'bx-message-square', 'bx-message-square-add', 'bx-message-square-check', 'bx-message-square-detail', 'bx-message-square-dots', 'bx-message-square-edit', 'bx-message-square-error', 'bx-message-square-minus', 'bx-message-square-x', 'bx-message-x', 'bx-meteor', 'bx-microchip', 'bx-microphone', 'bx-microphone-alt', 'bx-microphone-off', 'bx-minus', 'bx-minus-back', 'bx-minus-circle', 'bx-minus-front', 'bx-mobile', 'bx-mobile-alt', 'bx-mobile-landscape', 'bx-mobile-vibration', 'bx-money', 'bx-money-withdraw', 'bx-moon', 'bx-mouse', 'bx-mouse-alt', 'bx-move', 'bx-move-horizontal', 'bx-move-vertical', 'bx-movie', 'bx-movie-play', 'bx-music', 'bx-navigation', 'bx-network-chart', 'bx-news', 'bx-no-entry', 'bx-note', 'bx-notepad', 'bx-notification', 'bx-notification-off', 'bx-objects-horizontal-center', 'bx-objects-horizontal-left', 'bx-objects-horizontal-right', 'bx-objects-vertical-bottom', 'bx-objects-vertical-center', 'bx-objects-vertical-top', 'bx-package', 'bx-paint', 'bx-paint-roll', 'bx-palette', 'bx-paperclip', 'bx-paragraph', 'bx-party', 'bx-paste', 'bx-pause', 'bx-pause-circle', 'bx-pen', 'bx-pencil', 'bx-phone', 'bx-phone-call', 'bx-phone-incoming', 'bx-phone-off', 'bx-phone-outgoing', 'bx-photo-album', 'bx-pie-chart', 'bx-pie-chart-alt', 'bx-pie-chart-alt-2', 'bx-pin', 'bx-pizza', 'bx-plane', 'bx-plane-alt', 'bx-plane-land', 'bx-plane-take-off', 'bx-planet', 'bx-play', 'bx-play-circle', 'bx-plug', 'bx-plus', 'bx-plus-circle', 'bx-plus-medical', 'bx-podcast', 'bx-pointer', 'bx-poll', 'bx-polygon', 'bx-pool', 'bx-power-off', 'bx-printer', 'bx-pulse', 'bx-purchase-tag', 'bx-purchase-tag-alt', 'bx-pyramid', 'bx-qr', 'bx-qr-scan', 'bx-question-mark', 'bx-radio', 'bx-radio-circle', 'bx-radio-circle-marked', 'bx-receipt', 'bx-rectangle', 'bx-recycle', 'bx-redo', 'bx-reflect-horizontal', 'bx-reflect-vertical', 'bx-refresh', 'bx-registered', 'bx-rename', 'bx-repeat', 'bx-reply', 'bx-reply-all', 'bx-repost', 'bx-reset', 'bx-restaurant', 'bx-revision', 'bx-rewind', 'bx-rewind-circle', 'bx-right-arrow', 'bx-right-arrow-alt', 'bx-right-arrow-circle', 'bx-right-down-arrow-circle', 'bx-right-indent', 'bx-right-top-arrow-circle', 'bx-rocket', 'bx-rotate-left', 'bx-rotate-right', 'bx-rss', 'bx-ruler', 'bx-run', 'bx-sad', 'bx-save', 'bx-scan', 'bx-scatter-chart', 'bx-search', 'bx-search-alt', 'bx-search-alt-2', 'bx-selection', 'bx-send', 'bx-server', 'bx-shape-circle', 'bx-shape-polygon', 'bx-shape-square', 'bx-share', 'bx-share-alt', 'bx-shield', 'bx-shield-alt-2', 'bx-shield-quarter', 'bx-shield-x', 'bx-shocked', 'bx-shopping-bag', 'bx-show', 'bx-show-alt', 'bx-shuffle', 'bx-sidebar', 'bx-sitemap', 'bx-skip-next', 'bx-skip-next-circle', 'bx-skip-previous', 'bx-skip-previous-circle', 'bx-sleepy', 'bx-slider', 'bx-slider-alt', 'bx-slideshow', 'bx-smile', 'bx-sort', 'bx-sort-a-z', 'bx-sort-alt-2', 'bx-sort-down', 'bx-sort-up', 'bx-sort-z-a', 'bx-spa', 'bx-space-bar', 'bx-speaker', 'bx-spreadsheet', 'bx-square', 'bx-square-rounded', 'bx-star', 'bx-star-half', 'bx-station', 'bx-stats', 'bx-sticker', 'bx-stop', 'bx-stop-circle', 'bx-stopwatch', 'bx-store', 'bx-store-alt', 'bx-street-view', 'bx-strikethrough', 'bx-subdirectory-left', 'bx-subdirectory-right', 'bx-sun', 'bx-support', 'bx-swim', 'bx-sync', 'bx-tab', 'bx-table', 'bx-tablet', 'bx-tablet-landscape', 'bx-tachometer', 'bx-tag', 'bx-tag-alt', 'bx-target-lock', 'bx-task', 'bx-taxi', 'bx-tennis-ball', 'bx-terminal', 'bx-test-tube', 'bx-text', 'bx-time', 'bx-time-five', 'bx-timer', 'bx-tired', 'bx-toggle-left', 'bx-toggle-right', 'bx-tone', 'bx-traffic-cone', 'bx-train', 'bx-transfer', 'bx-transfer-alt', 'bx-trash', 'bx-trash-alt', 'bx-trending-down', 'bx-trending-up', 'bx-trim', 'bx-trip', 'bx-trophy', 'bx-truck', 'bx-tv', 'bx-type', 'bx-type-bold', 'bx-type-italic', 'bx-type-underline', 'bx-underline', 'bx-undo', 'bx-unlink', 'bx-up-arrow', 'bx-up-arrow-alt', 'bx-up-arrow-circle', 'bx-upload', 'bx-upside-down', 'bx-upvote', 'bx-usb', 'bx-user', 'bx-user-check', 'bx-user-circle', 'bx-user-minus', 'bx-user-pin', 'bx-user-plus', 'bx-user-voice', 'bx-user-x', 'bx-vector', 'bx-vertical-center', 'bx-vertical-top', 'bx-video', 'bx-video-off', 'bx-video-plus', 'bx-video-recording', 'bx-voicemail', 'bx-volume', 'bx-volume-full', 'bx-volume-low', 'bx-volume-mute', 'bx-walk', 'bx-wallet', 'bx-wallet-alt', 'bx-water', 'bx-webcam', 'bx-wifi', 'bx-wifi-0', 'bx-wifi-1', 'bx-wifi-2', 'bx-wifi-off', 'bx-wind', 'bx-window', 'bx-window-alt', 'bx-window-close', 'bx-window-open', 'bx-wine', 'bx-wink-smile', 'bx-wink-tongue', 'bx-won', 'bx-world', 'bx-wrench', 'bx-x', 'bx-x-circle', 'bx-yen', 'bx-zoom-in', 'bx-zoom-out'
    ];

    // Brand icons (bxl-) - extracted from CDN
    $brandIcons = [
        'bxl-500px', 'bxl-99designs', 'bxl-adobe', 'bxl-airbnb', 'bxl-algolia', 'bxl-amazon', 'bxl-android', 'bxl-angular', 'bxl-anthropic', 'bxl-apple-music', 'bxl-apple', 'bxl-arc-browser', 'bxl-artstation', 'bxl-asana', 'bxl-atlassian', 'bxl-atom-editor', 'bxl-audible', 'bxl-auth0', 'bxl-autodesk', 'bxl-aws', 'bxl-baidu', 'bxl-bash', 'bxl-behance', 'bxl-better-auth', 'bxl-bing', 'bxl-bitcoin-logo', 'bxl-blender', 'bxl-blogger', 'bxl-bluesky', 'bxl-bolt-b', 'bxl-bootstrap', 'bxl-boxicons', 'bxl-brave-browser', 'bxl-bun', 'bxl-buy-me-a-coffee', 'bxl-c-plus-plus', 'bxl-c-sharp', 'bxl-c', 'bxl-canva', 'bxl-chess-com', 'bxl-chrome', 'bxl-claude-ai', 'bxl-clerk', 'bxl-cloudflare', 'bxl-codepen', 'bxl-convex', 'bxl-creative-commons', 'bxl-crunchyroll', 'bxl-css3', 'bxl-dailymotion', 'bxl-deepmind', 'bxl-deepseek', 'bxl-deezer', 'bxl-deno', 'bxl-dev-to', 'bxl-deviantart', 'bxl-devpost', 'bxl-digg', 'bxl-digitalocean', 'bxl-discord-alt', 'bxl-discord', 'bxl-discourse', 'bxl-django', 'bxl-docker', 'bxl-dot-env', 'bxl-dribbble', 'bxl-drizzle-orm', 'bxl-dropbox', 'bxl-ebay', 'bxl-edge', 'bxl-etsy', 'bxl-expo', 'bxl-express-js', 'bxl-facebook-circle', 'bxl-facebook-square', 'bxl-facebook', 'bxl-fastapi', 'bxl-fastify', 'bxl-figma-alt', 'bxl-figma', 'bxl-firebase', 'bxl-firefox', 'bxl-fiverr', 'bxl-flask-old', 'bxl-flask', 'bxl-flickr-square', 'bxl-flickr', 'bxl-flutter', 'bxl-foursquare', 'bxl-framer', 'bxl-gatsby-js', 'bxl-gemini', 'bxl-git', 'bxl-github-copilot', 'bxl-github', 'bxl-gitlab', 'bxl-gmail', 'bxl-go-lang', 'bxl-google-cloud', 'bxl-google-pay', 'bxl-google', 'bxl-graphql', 'bxl-grok', 'bxl-groq-ai', 'bxl-gsap', 'bxl-gumroad', 'bxl-hashnode', 'bxl-hcaptcha', 'bxl-heroku', 'bxl-hono-js', 'bxl-html5', 'bxl-hugo', 'bxl-ibm', 'bxl-imdb', 'bxl-instagram-alt', 'bxl-instagram', 'bxl-internet-explorer', 'bxl-invision', 'bxl-java', 'bxl-javascript', 'bxl-joomla', 'bxl-jquery', 'bxl-jsfiddle', 'bxl-jwt', 'bxl-kick', 'bxl-kickstarter', 'bxl-kotlin', 'bxl-kubernetes', 'bxl-laravel', 'bxl-leetcode', 'bxl-lemon-squeezy', 'bxl-less', 'bxl-letterboxd', 'bxl-lichess', 'bxl-linear-app', 'bxl-linkedin-square', 'bxl-linkedin', 'bxl-linktree', 'bxl-loom', 'bxl-lottie-lab', 'bxl-lyft', 'bxl-magento', 'bxl-mailchimp', 'bxl-markdown', 'bxl-mastercard', 'bxl-mastodon', 'bxl-medium-old', 'bxl-medium-square', 'bxl-medium', 'bxl-messenger', 'bxl-meta', 'bxl-microsoft-teams', 'bxl-microsoft-windows', 'bxl-microsoft', 'bxl-midjourney', 'bxl-mongodb', 'bxl-motion-js', 'bxl-mozilla', 'bxl-my-sql', 'bxl-neon-tech', 'bxl-neovim', 'bxl-nest-js', 'bxl-netlify', 'bxl-next-js', 'bxl-nodejs', 'bxl-notion', 'bxl-npm', 'bxl-nuxt-js', 'bxl-ok-ru', 'bxl-ollama', 'bxl-openai', 'bxl-opensea', 'bxl-opera', 'bxl-paddle-p', 'bxl-patreon', 'bxl-payload-cms', 'bxl-paypal', 'bxl-periscope', 'bxl-perplexity-ai', 'bxl-php', 'bxl-pinterest-alt', 'bxl-pinterest', 'bxl-planetscale', 'bxl-play-store', 'bxl-playstation', 'bxl-pocket', 'bxl-polar', 'bxl-postgresql', 'bxl-prisma-orm', 'bxl-product-hunt', 'bxl-python', 'bxl-quora', 'bxl-radix-ui', 'bxl-railway', 'bxl-rasberry-pi', 'bxl-react-query', 'bxl-react-router', 'bxl-react', 'bxl-redbubble', 'bxl-reddit', 'bxl-redux', 'bxl-remix-js', 'bxl-replit', 'bxl-resend', 'bxl-roblox', 'bxl-sass', 'bxl-sentry', 'bxl-shadcn-ui', 'bxl-shopify', 'bxl-sketch', 'bxl-skype', 'bxl-slack-old', 'bxl-slack', 'bxl-snapchat', 'bxl-socket-io', 'bxl-soundcloud', 'bxl-spotify', 'bxl-spring-boot', 'bxl-squarespace', 'bxl-sst', 'bxl-stack-overflow', 'bxl-stackblitz', 'bxl-steam', 'bxl-stripe', 'bxl-supabase', 'bxl-svelte', 'bxl-tailwind-css', 'bxl-telegram', 'bxl-terraform', 'bxl-threads', 'bxl-three-js', 'bxl-tiktok', 'bxl-trello', 'bxl-trip-advisor', 'bxl-trpc', 'bxl-trustpilot', 'bxl-tumblr', 'bxl-tux', 'bxl-twitch', 'bxl-twitter-x', 'bxl-twitter', 'bxl-typescript', 'bxl-uber', 'bxl-ubuntu', 'bxl-udacity', 'bxl-unity', 'bxl-unsplash', 'bxl-upwork', 'bxl-v0', 'bxl-venmo', 'bxl-vercel', 'bxl-vimeo', 'bxl-visa', 'bxl-visual-studio', 'bxl-vite-js', 'bxl-vk', 'bxl-vuejs', 'bxl-waze', 'bxl-web-components', 'bxl-webflow', 'bxl-weibo', 'bxl-whatsapp-square', 'bxl-whatsapp', 'bxl-wikipedia', 'bxl-windsurf', 'bxl-wix', 'bxl-wordpress', 'bxl-work-os', 'bxl-xai', 'bxl-xbox', 'bxl-xing', 'bxl-yahoo', 'bxl-yarn', 'bxl-yelp', 'bxl-youtube-music', 'bxl-youtube', 'bxl-zen-browser', 'bxl-zoom-workplace'
    ];

    $boxicons = array_merge($regularIcons, $brandIcons);

    $boxicons = array_unique($boxicons);
    sort($boxicons);

    // Determine which icon set is currently selected and extract the specific icon class
    $selectedIconSet = 'remix';
    $selectedSpecificIcon = $selected;

    if (strpos($selected, 'bx') === 0) {
        $selectedIconSet = 'boxicons';
        // Extract the specific icon class from complete Boxicon class (e.g., 'bx bx-table' -> 'bx-table')
        if (strpos($selected, 'bx ') === 0) {
            $selectedSpecificIcon = substr($selected, 3); // Remove 'bx ' prefix
        }
    }
@endphp

<div class="mb-3" id="{{ $componentId }}">
    <label class="form-label" for="{{ $inputId }}">
        {{ $label }}
        @if($required)
            <span class="text-danger">*</span>
        @endif
    </label>

    <!-- Unified Icon Picker Container -->
    <div class="icon-picker-container border rounded">
        <!-- Icon Set Button Group Header -->
        <div class="icon-picker-header d-flex justify-content-left p-3 border-bottom bg-light rounded-top">
            <div class="btn-group" id="{{ $tabsId }}" role="group" aria-label="Icon set selection">
                <input type="radio" class="btn-check" name="{{ $componentId }}-icon-set" id="{{ $remixTabId }}"
                       autocomplete="off" {{ $selectedIconSet === 'remix' ? 'checked' : '' }}
                       data-bs-toggle="tab" data-bs-target="#{{ $remixContentId }}"
                       aria-controls="{{ $remixContentId }}" aria-selected="{{ $selectedIconSet === 'remix' ? 'true' : 'false' }}">
                <label class="btn btn-outline-secondary" for="{{ $remixTabId }}">
                    <i class="ri-remixicon-line me-2"></i>Remix Icons ({{ count($remixIcons) }})
                </label>

                <input type="radio" class="btn-check" name="{{ $componentId }}-icon-set" id="{{ $boxiconsTabId }}"
                       autocomplete="off" {{ $selectedIconSet === 'boxicons' ? 'checked' : '' }}
                       data-bs-toggle="tab" data-bs-target="#{{ $boxiconsContentId }}"
                       aria-controls="{{ $boxiconsContentId }}" aria-selected="{{ $selectedIconSet === 'boxicons' ? 'true' : 'false' }}">
                <label class="btn btn-outline-secondary" for="{{ $boxiconsTabId }}">
                    <i class="bx bx-box me-2"></i>Boxicons ({{ count($boxicons) }})
                </label>
            </div>
        </div>

        <!-- Icon Set Content -->
        <div class="tab-content" id="{{ $componentId }}-tab-content">
            <!-- Remix Icons Tab -->
            <div class="tab-pane fade {{ $selectedIconSet === 'remix' ? 'show active' : '' }}"
                 id="{{ $remixContentId }}" role="tabpanel" aria-labelledby="{{ $remixTabId }}">
                <div class="d-flex flex-wrap gap-3 icon-picker"
                     style="max-height: 300px; overflow-y: auto; padding: 8px;" data-icon-set="remix">
                    @if(!empty($remixIcons))
                        @foreach ($remixIcons as $icon)
                            <div class="icon-option border rounded p-2 {{ $selected === $icon ? 'border-primary' : '' }}"
                                style="cursor: pointer; min-width: 50px; text-align: center;"
                                data-icon="{{ $icon }}" title="{{ $icon }}">
                                <i class="{{ $icon }} fs-4 d-block mb-1"></i>
                                {{-- <small class="text-muted">{{ substr($icon, 3, 4) }}...</small> --}}
                            </div>
                        @endforeach
                    @else
                        <div class="text-muted p-3">
                            <i class="ri-alert-line me-2"></i>
                            Remix Icons not found. Please check the installation.
                        </div>
                    @endif
                </div>
            </div>

            <!-- Boxicons Tab -->
            <div class="tab-pane fade {{ $selectedIconSet === 'boxicons' ? 'show active' : '' }}"
                 id="{{ $boxiconsContentId }}" role="tabpanel" aria-labelledby="{{ $boxiconsTabId }}">
                <div class="d-flex flex-wrap gap-3 icon-picker"
                     style="max-height: 300px; overflow-y: auto; padding: 8px;" data-icon-set="boxicons">
                    @if(!empty($boxicons))
                        @foreach ($boxicons as $icon)
                            <div class="icon-option border rounded p-2 {{ $selectedSpecificIcon === $icon ? 'border-primary' : '' }}"
                                style="cursor: pointer; min-width: 50px; text-align: center;"
                                data-icon="{{ $icon }}" title="{{ $icon }}">
                                @if(str_starts_with($icon, 'bxl-'))
                                    <i class="bxl {{ $icon }} fs-4 d-block mb-1"></i>
                                @else
                                    <i class="bx {{ $icon }} fs-4 d-block mb-1"></i>
                                @endif
                                {{-- <small class="text-muted">{{ substr($icon, 3, 4) }}...</small> --}}
                            </div>
                        @endforeach
                    @else
                        <div class="text-muted p-3">
                            <i class="bx bx-error me-2"></i>
                            Boxicons not found. Please check the installation.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <input type="hidden" name="{{ $name }}" id="{{ $inputId }}" value="{{ $selected }}" @if($required) required @endif>
    
    @error($name)
        <div class="text-danger mt-1">{{ $message }}</div>
    @enderror

    <div class="form-text">
        Click an icon to select it. Currently selected: <code id="{{ $displayId }}">{{ $selected }}</code>
    </div>
</div>

@once
    @push('styles')
        <style>
            /* Ensure Remix Icons display correctly */
            [class^="ri-"], [class*=" ri-"] {
                font-family: 'remixicon' !important;
                font-style: normal !important;
                font-weight: normal !important;
                font-variant: normal !important;
                text-transform: none !important;
                line-height: 1 !important;
                -webkit-font-smoothing: antialiased !important;
                -moz-osx-font-smoothing: grayscale !important;
                display: inline-block !important;
            }
            
            /* Ensure Boxicons display correctly */
            [class^="bx"], [class*=" bx"] {
                font-family: 'boxicons' !important;
                font-style: normal !important;
                font-weight: normal !important;
                font-variant: normal !important;
                text-transform: none !important;
                line-height: 1 !important;
                -webkit-font-smoothing: antialiased !important;
                -moz-osx-font-smoothing: grayscale !important;
                display: inline-block !important;
            }

            /* Ensure Boxicons brand icons display correctly */
            [class^="bxl"], [class*=" bxl"] {
                font-family: 'boxicons-brands' !important;
                font-style: normal !important;
                font-weight: normal !important;
                font-variant: normal !important;
                text-transform: none !important;
                line-height: 1 !important;
                -webkit-font-smoothing: antialiased !important;
                -moz-osx-font-smoothing: grayscale !important;
                display: inline-block !important;
            }
            
            /* Enhanced icon picker styles */
            .icon-picker {
                padding: 8px !important;
            }

            .icon-picker .icon-option {
                transition: all 0.2s ease;
                min-height: 60px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                margin: 2px;
            }
            
            .icon-picker .icon-option:hover {
                background-color: #f8f9fa;
                border-color: rgb(var(--secondary-rgb)) !important;
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .icon-picker .icon-option.border-primary {
                background-color: rgb(var(--secondary-rgb)) !important;
                border-color: rgb(var(--secondary-rgb)) !important;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(var(--secondary-rgb), 0.4);
            }

            .icon-picker .icon-option.border-primary i {
                color: white !important;
            }

            .icon-picker .icon-option.border-primary small {
                color: rgba(255, 255, 255, 0.8) !important;
            }

            .icon-picker .icon-option.border-primary:hover {
                background-color: rgba(var(--secondary-rgb), 0.9) !important;
                border-color: rgb(var(--secondary-rgb)) !important;
                box-shadow: 0 3px 10px rgba(var(--secondary-rgb), 0.5);
            }
            
            /* Force icon display */
            .icon-picker .icon-option i {
                font-size: 1.5rem !important;
                color: #333 !important;
            }
            
            /* Unified icon picker container */
            .icon-picker-container {
                background-color: #fff;
                border-color: #dee2e6 !important;
            }

            .icon-picker-header {
                background-color: #f8f9fa !important;
                border-bottom-color: #dee2e6 !important;
                border-top-left-radius: 0.375rem !important;
                border-top-right-radius: 0.375rem !important;
            }

            /* Button group styling */
            .btn-group .btn-outline-secondary {
                border-color: #dee2e6;
                color: #6c757d;
                transition: all 0.2s ease;
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }

            .btn-group .btn-outline-secondary:hover {
                background-color: rgba(var(--secondary-rgb), 0.1);
                border-color: rgb(var(--secondary-rgb));
                color: rgb(var(--secondary-rgb));
            }

            .btn-group .btn-check:checked + .btn-outline-secondary {
                background-color: rgb(var(--secondary-rgb));
                border-color: rgb(var(--secondary-rgb));
                color: white !important;
                box-shadow: 0 2px 4px rgba(var(--secondary-rgb), 0.2);
            }

            .btn-group .btn-check:focus + .btn-outline-secondary {
                box-shadow: 0 0 0 0.2rem rgba(var(--secondary-rgb), 0.25);
            }

            /* Natural button sizing with proper padding */
            .btn-group .btn {
                white-space: nowrap;
            }

            /* Remove all border styling from tab content areas
            /* Ensure tab panes have no borders */
            .tab-content .tab-pane {
                border: 0 !important;
                border-radius: 0 !important;
                border-width: 0 !important;
                box-shadow: none !important;
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Initialize icon selector for component: {{ $componentId }}
                const componentId = '{{ $componentId }}';
                const inputId = '{{ $inputId }}';
                const displayId = '{{ $displayId }}';
                const tabsId = '{{ $tabsId }}';

                const iconInput = document.getElementById(inputId);
                const selectedIconDisplay = document.getElementById(displayId);
                const componentElement = document.getElementById(componentId);

                if (!componentElement) return; // Component not found

                // Function to handle icon selection within this component
                function handleIconSelection() {
                    const iconOptions = componentElement.querySelectorAll('.icon-option');

                    iconOptions.forEach(option => {
                        // Remove existing listeners to prevent duplicates
                        option.replaceWith(option.cloneNode(true));
                    });

                    // Re-select the options after cloning
                    const refreshedIconOptions = componentElement.querySelectorAll('.icon-option');

                    refreshedIconOptions.forEach(option => {
                        option.addEventListener('click', function() {
                            // Remove active style from all icons in this component
                            componentElement.querySelectorAll('.icon-option').forEach(opt => opt.classList.remove('border-primary'));

                            // Add active style to clicked icon
                            this.classList.add('border-primary');

                            // Set hidden input and display
                            const selectedIcon = this.getAttribute('data-icon');

                            // For Boxicons, we need to add the appropriate base class
                            let iconClassToStore = selectedIcon;
                            if (selectedIcon.startsWith('bx-')) {
                                iconClassToStore = 'bx ' + selectedIcon;
                            } else if (selectedIcon.startsWith('bxl-')) {
                                iconClassToStore = 'bxl ' + selectedIcon;
                            }

                            if (iconInput) {
                                iconInput.value = iconClassToStore;
                            }
                            if (selectedIconDisplay) {
                                selectedIconDisplay.textContent = iconClassToStore;
                            }

                            console.log('Selected icon in ' + componentId + ':', selectedIcon);
                        });
                    });
                }

                // Initialize icon selection handlers
                handleIconSelection();

                // Re-initialize handlers when button group tab is switched
                const tabInputs = componentElement.querySelectorAll('input[data-bs-toggle="tab"]');
                tabInputs.forEach(input => {
                    input.addEventListener('change', function() {
                        if (this.checked) {
                            // Trigger Bootstrap tab change
                            const targetId = this.getAttribute('data-bs-target');
                            const targetPane = document.querySelector(targetId);

                            // Hide all tab panes in this component
                            componentElement.querySelectorAll('.tab-pane').forEach(pane => {
                                pane.classList.remove('show', 'active');
                            });

                            // Show the selected tab pane
                            if (targetPane) {
                                targetPane.classList.add('show', 'active');
                            }

                            // Update aria-selected attributes
                            componentElement.querySelectorAll('input[data-bs-toggle="tab"]').forEach(inp => {
                                inp.setAttribute('aria-selected', 'false');
                            });
                            this.setAttribute('aria-selected', 'true');

                            // Small delay to ensure tab content is fully loaded
                            setTimeout(handleIconSelection, 100);
                        }
                    });
                });
            });
        </script>
    @endpush
@endonce
